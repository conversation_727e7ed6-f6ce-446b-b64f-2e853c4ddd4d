import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Test function to verify Convex is working
export const test = query({
  args: {},
  handler: async () => {
    return { message: "Convex is working!" };
  },
});

// Simple authentication functions
export const signUp = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    // Server-side validation
    if (!args.email || !args.email.includes('@')) {
      throw new Error("Please provide a valid email address");
    }

    if (!args.password || args.password.length < 6) {
      throw new Error("Password must be at least 6 characters long");
    }

    if (!args.name || args.name.length < 2) {
      throw new Error("Name must be at least 2 characters long");
    }

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email.toLowerCase()))
      .first();

    if (existingUser) {
      throw new Error("User already exists with this email address");
    }

    // Create user (in a real app, you'd hash the password)
    const userId = await ctx.db.insert("users", {
      email: args.email.toLowerCase(),
      name: args.name.trim(),
      passwordHash: args.password, // This should be hashed in production
    });

    return { userId, email: args.email.toLowerCase(), name: args.name.trim() };
  },
});

export const signIn = mutation({
  args: {
    email: v.string(),
    password: v.string(),
  },
  handler: async (ctx, args) => {
    // Server-side validation
    if (!args.email || !args.email.includes('@')) {
      throw new Error("Please provide a valid email address");
    }

    if (!args.password) {
      throw new Error("Password is required");
    }

    // Find user
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email.toLowerCase()))
      .first();

    if (!user) {
      throw new Error("No account found with this email address");
    }

    if (user.passwordHash !== args.password) {
      throw new Error("Invalid password");
    }

    return { userId: user._id, email: user.email, name: user.name };
  },
});
