import { v } from "convex/values";
import { mutation, query } from "./_generated/server";

// Simple authentication functions
export const signUp = mutation({
  args: {
    email: v.string(),
    password: v.string(),
    name: v.string(),
  },
  handler: async (ctx, args) => {
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (existingUser) {
      throw new Error("User already exists");
    }

    // Create user (in a real app, you'd hash the password)
    const userId = await ctx.db.insert("users", {
      email: args.email,
      name: args.name,
      passwordHash: args.password, // This should be hashed in production
    });

    return { userId, email: args.email, name: args.name };
  },
});

export const signIn = mutation({
  args: {
    email: v.string(),
    password: v.string(),
  },
  handler: async (ctx, args) => {
    // Find user
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (!user || user.passwordHash !== args.password) {
      throw new Error("Invalid credentials");
    }

    return { userId: user._id, email: user.email, name: user.name };
  },
});
