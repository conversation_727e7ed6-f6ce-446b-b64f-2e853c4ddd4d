
import React from 'react';
import Button from '../ui/Button';
import { Page, User, Workspace } from '../../types';
import WorkspaceSwitcher from './WorkspaceSwitcher';
import { isAIAvailable } from '../../services/geminiService';

interface HeaderProps {
  currentPage: Page;
  onOpenAiAssistant: () => void;
  onOpenSidebar: () => void;
  user: User | null;
  workspaces: Workspace[];
  activeWorkspace: Workspace | null;
  onSwitchWorkspace: (workspaceId: string) => void;
  onLogout: () => void;
}

const Header: React.FC<HeaderProps> = ({ currentPage, onOpenAiAssistant, onOpenSidebar, user, workspaces, activeWorkspace, onSwitchWorkspace, onLogout }) => {
  const aiDisabledTooltip = !isAIAvailable ? 'AI features are unavailable. API key not configured.' : '';
  
  return (
    <header className="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4 sm:px-6 flex-shrink-0">
      <div className="flex items-center">
        <button
          onClick={onOpenSidebar}
          className="lg:hidden mr-3 text-gray-600 hover:text-gray-900 focus:outline-none"
          aria-label="Open sidebar"
        >
          <svg className="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
        <h2 className="text-lg font-semibold text-gray-900 truncate">{currentPage}</h2>
      </div>
      <div className="flex items-center space-x-2 sm:space-x-4">
        <Button 
          onClick={onOpenAiAssistant} 
          variant="primary" 
          className="p-2 sm:px-4 sm:py-2 text-sm"
          disabled={!isAIAvailable}
          title={aiDisabledTooltip}
        >
          <div className="flex items-center justify-center">
             <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
               <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.938V19a1 1 0 11-2 0v-4.062a4.5 4.5 0 112 0z" clipRule="evenodd" />
             </svg>
            <span className="hidden sm:inline ml-2">Ask AI</span>
          </div>
        </Button>
        <WorkspaceSwitcher
            user={user}
            workspaces={workspaces}
            activeWorkspace={activeWorkspace}
            onSwitchWorkspace={onSwitchWorkspace}
            onLogout={onLogout}
        />
        <Button
          onClick={onLogout}
          variant="ghost"
          className="p-2 sm:px-3 sm:py-2 text-sm text-gray-600 hover:text-gray-900"
          title="Logout (Ctrl+Shift+L)"
        >
          <div className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z" clipRule="evenodd" />
            </svg>
            <span className="hidden sm:inline ml-2">Logout</span>
          </div>
        </Button>
      </div>
    </header>
  );
};

export default Header;
