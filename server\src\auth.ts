import { Router } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';

const router = Router();

// In-memory storage for demo purposes
// In production, you would use a proper database
interface User {
  id: string;
  email: string;
  password: string;
  businessId: string;
}

interface Business {
  id: string;
  name: string;
  phoneNumber?: string;
  physicalAddress?: string;
}

const users: User[] = [];
const businesses: Business[] = [];
let userIdCounter = 1;
let businessIdCounter = 1;

// Add a demo user for testing
const initializeDemoData = async () => {
  if (users.length === 0) {
    // Create demo business
    const demoBusiness: Business = {
      id: '1',
      name: 'Demo Company',
      phoneNumber: '************',
      physicalAddress: '123 Demo Street, Demo City, DC 12345',
    };
    businesses.push(demoBusiness);
    businessIdCounter = 2;

    // Create demo user (password: "demo123")
    const hashedPassword = await bcrypt.hash('demo123', 10);
    const demoUser: User = {
      id: '1',
      email: '<EMAIL>',
      password: hashedPassword,
      businessId: '1',
    };
    users.push(demoUser);
    userIdCounter = 2;

    console.log('Demo user created: <EMAIL> / demo123');
  }
};

// Initialize demo data
initializeDemoData();

router.post('/register', async (req, res) => {
  const { email, password, businessName, phoneNumber, physicalAddress } = req.body;
  if (!email || !password || !businessName) {
    return res.status(400).json({ message: 'Email, password, and business name are required' });
  }

  try {
    // Check if user already exists
    const existingUser = users.find(u => u.email === email);
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Create business
    const businessId = businessIdCounter.toString();
    businessIdCounter++;
    const newBusiness: Business = {
      id: businessId,
      name: businessName,
      phoneNumber: phoneNumber || undefined,
      physicalAddress: physicalAddress || undefined,
    };
    businesses.push(newBusiness);

    // Create user
    const hashedPassword = await bcrypt.hash(password, 10);
    const userId = userIdCounter.toString();
    userIdCounter++;
    const newUser: User = {
      id: userId,
      email,
      password: hashedPassword,
      businessId,
    };
    users.push(newUser);

    res.status(201).json({ message: 'User and business created successfully' });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Error creating user', error: error instanceof Error ? error.message : 'Unknown error' });
  }
});

router.post('/login', async (req, res) => {
  const { email, password } = req.body;
  if (!email || !password) {
    return res.status(400).json({ message: 'Email and password are required' });
  }

  try {
    // Find user by email
    const user = users.find(u => u.email === email);
    if (!user) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Generate JWT token
    const token = jwt.sign(
      { id: user.id, email: user.email, businessId: user.businessId },
      process.env.JWT_SECRET || 'your_jwt_secret',
      { expiresIn: '1h' }
    );

    res.json({ token });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Error logging in', error: error instanceof Error ? error.message : 'Unknown error' });
  }
});

export default router;