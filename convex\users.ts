import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { getAuthUserId } from "@convex-dev/auth/server";

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }
    return await ctx.db.get(userId);
  },
});

export const createUserProfile = mutation({
  args: {
    name: v.string(),
    businessName: v.optional(v.string()),
    phoneNumber: v.optional(v.string()),
    physicalAddress: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Update user with additional profile information
    await ctx.db.patch(userId, {
      name: args.name,
    });

    // Create a default workspace for the user
    const workspaceId = await ctx.db.insert("workspaces", {
      name: args.businessName || `${args.name}'s Workspace`,
      ownerId: userId,
    });

    // Add user as team member to their own workspace
    await ctx.db.insert("teamMembers", {
      workspaceId,
      userId,
      name: args.name,
      email: user.email,
      role: "Admin",
      status: "Active",
    });

    return { userId, workspaceId };
  },
});

export const getUserWorkspaces = query({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return [];
    }

    const teamMemberships = await ctx.db
      .query("teamMembers")
      .filter((q) => q.eq(q.field("userId"), userId))
      .collect();

    const workspaces = await Promise.all(
      teamMemberships.map(async (membership) => {
        const workspace = await ctx.db.get(membership.workspaceId);
        return workspace;
      })
    );

    return workspaces.filter(Boolean);
  },
});