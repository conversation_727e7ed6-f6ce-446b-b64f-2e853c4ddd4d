import React, { useState } from 'react';
import Button from '../components/ui/Button';
import { useMutation, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";

const AuthHeader = () => (
  <header className="absolute top-0 left-0 right-0 z-10">
    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center h-20">
        <div className="flex items-center">
          <a href="/" className="text-2xl font-bold tracking-tighter">FinHog</a>
        </div>
      </div>
    </div>
  </header>
);

const Footer = () => (
    <footer className="bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <p className="text-center text-sm text-muted-foreground">
                &copy; {new Date().getFullYear()} FinHog, Inc. All rights reserved.
            </p>
        </div>
    </footer>
)

const AuthPage: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    businessName: '',
    phoneNumber: '',
    physicalAddress: '',
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  const signUp = useMutation(api.auth.signUp);
  const signIn = useMutation(api.auth.signIn);
  const createUserProfile = useMutation(api.users.createUserProfile);

  // Test Convex connection
  const testResult = useQuery(api.auth.test);

  // Debug: Check if mutations are available
  console.log('Mutations available:', { signUp, signIn, createUserProfile });
  console.log('Test result:', testResult);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    console.log('Form submitted:', { isLogin, formData });

    try {
      if (isLogin) {
        console.log('Attempting to sign in...');
        // Sign in with email and password
        const result = await signIn({
          email: formData.email,
          password: formData.password,
        });

        console.log('Sign in result:', result);

        // Store user ID in localStorage
        localStorage.setItem('userId', result.userId);

        // Redirect to dashboard
        window.location.href = '/dashboard';
      } else {
        console.log('Attempting to sign up...');
        // Registration - sign up and create profile
        if (!formData.email || !formData.password || !formData.businessName) {
          throw new Error('Please fill in all required fields');
        }

        // Sign up with email and password
        const result = await signUp({
          email: formData.email,
          password: formData.password,
          name: formData.businessName,
        });

        console.log('Sign up result:', result);

        // Store user ID in localStorage
        localStorage.setItem('userId', result.userId);

        // Create user profile with additional information
        await createUserProfile({
          userId: result.userId,
          businessName: formData.businessName,
          phoneNumber: formData.phoneNumber || undefined,
          physicalAddress: formData.physicalAddress || undefined,
        });

        // Redirect to dashboard
        window.location.href = '/dashboard';
      }
    } catch (err: any) {
      console.error('Authentication error:', err);
      setError(err.message || 'An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-background text-foreground antialiased">
      <AuthHeader />
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-40">
        <div className="max-w-md mx-auto">
          <h1 className="text-4xl font-bold tracking-tighter mb-4 text-center">{isLogin ? 'Log In' : 'Sign Up'}</h1>

          {/* Debug info */}
          {testResult && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 text-sm">
              ✅ Convex connected: {testResult.message}
            </div>
          )}

          {!testResult && (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4 text-sm">
              ⏳ Connecting to Convex...
            </div>
          )}

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-y-6">
              <div>
                <label htmlFor="email" className="sr-only">Email</label>
                <input id="email" name="email" type="email" autoComplete="email" placeholder="Email" required className="block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary border-gray-300 rounded-md" value={formData.email} onChange={handleChange} />
              </div>
              <div>
                <label htmlFor="password" className="sr-only">Password</label>
                <input id="password" name="password" type="password" autoComplete="current-password" placeholder="Password" required className="block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary border-gray-300 rounded-md" value={formData.password} onChange={handleChange} />
              </div>
              {!isLogin && (
                <>
                  <div>
                    <label htmlFor="businessName" className="sr-only">Business Name</label>
                    <input id="businessName" name="businessName" type="text" placeholder="Business Name" required className="block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary border-gray-300 rounded-md" value={formData.businessName} onChange={handleChange} />
                  </div>
                  <div>
                    <label htmlFor="phoneNumber" className="sr-only">Phone Number</label>
                    <input id="phoneNumber" name="phoneNumber" type="text" placeholder="Phone Number" className="block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary border-gray-300 rounded-md" value={formData.phoneNumber} onChange={handleChange} />
                  </div>
                  <div>
                    <label htmlFor="physicalAddress" className="sr-only">Physical Address (Optional)</label>
                    <input id="physicalAddress" name="physicalAddress" type="text" placeholder="Physical Address (Optional)" className="block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary border-gray-300 rounded-md" value={formData.physicalAddress} onChange={handleChange} />
                  </div>
                </>
              )}
              {error && <p className="text-red-500 text-sm">{error}</p>}
              <div>
                <Button type="submit" size="lg" className="w-full flex justify-center items-center gap-4" disabled={loading}>
                  {loading ? 'Please wait...' : (isLogin ? 'Log In' : 'Sign Up')}
                </Button>
              </div>
            </div>
          </form>
          <div className="text-center mt-4">
            <button onClick={() => setIsLogin(!isLogin)} className="text-sm text-primary hover:underline">
              {isLogin ? 'Need an account? Sign Up' : 'Already have an account? Log In'}
            </button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AuthPage;