import React, { useState } from 'react';
import Button from '../components/ui/Button';
import { useMutation, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";

const AuthHeader = () => (
  <header className="absolute top-0 left-0 right-0 z-10">
    <div className="container mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center h-20">
        <div className="flex items-center">
          <a href="/" className="text-2xl font-bold tracking-tighter">FinHog</a>
        </div>
      </div>
    </div>
  </header>
);

const Footer = () => (
    <footer className="bg-background">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <p className="text-center text-sm text-muted-foreground">
                &copy; {new Date().getFullYear()} FinHog, Inc. All rights reserved.
            </p>
        </div>
    </footer>
)

const AuthPage: React.FC = () => {
  const [isLogin, setIsLogin] = useState(true);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    businessName: '',
    phoneNumber: '',
    physicalAddress: '',
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [fieldErrors, setFieldErrors] = useState<{[key: string]: string}>({});
  const [success, setSuccess] = useState('');

  const signUp = useMutation(api.auth.signUp);
  const signIn = useMutation(api.auth.signIn);
  const createUserProfile = useMutation(api.users.createUserProfile);

  // Test Convex connection
  const testResult = useQuery(api.auth.test);

  // Mutations are ready when testResult is available

  // Validation functions
  const validateEmail = (email: string): string | null => {
    if (!email) return 'Email is required';
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) return 'Please enter a valid email address';
    return null;
  };

  const validatePassword = (password: string): string | null => {
    if (!password) return 'Password is required';
    if (password.length < 6) return 'Password must be at least 6 characters long';
    if (password.length > 100) return 'Password must be less than 100 characters';
    return null;
  };

  const validateBusinessName = (name: string): string | null => {
    if (!name) return 'Business name is required';
    if (name.length < 2) return 'Business name must be at least 2 characters long';
    if (name.length > 100) return 'Business name must be less than 100 characters';
    return null;
  };

  const validatePhoneNumber = (phone: string): string | null => {
    if (!phone) return null; // Optional field
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    if (!phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ''))) {
      return 'Please enter a valid phone number';
    }
    return null;
  };

  const validateForm = (): boolean => {
    const errors: {[key: string]: string} = {};

    // Email validation
    const emailError = validateEmail(formData.email);
    if (emailError) errors.email = emailError;

    // Password validation
    const passwordError = validatePassword(formData.password);
    if (passwordError) errors.password = passwordError;

    // Business name validation (only for signup)
    if (!isLogin) {
      const businessNameError = validateBusinessName(formData.businessName);
      if (businessNameError) errors.businessName = businessNameError;

      // Phone number validation (optional but validate if provided)
      const phoneError = validatePhoneNumber(formData.phoneNumber);
      if (phoneError) errors.phoneNumber = phoneError;
    }

    setFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Clear field error when user starts typing
    if (fieldErrors[name]) {
      setFieldErrors({ ...fieldErrors, [name]: '' });
    }

    // Clear general error
    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setFieldErrors({});

    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      if (isLogin) {
        // Sign in with email and password
        const result = await signIn({
          email: formData.email,
          password: formData.password,
        });



        // Store user ID in localStorage
        localStorage.setItem('userId', result.userId);

        // Show success message briefly before redirect
        setSuccess('Login successful! Redirecting...');

        // Redirect to dashboard after a brief delay
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 1000);
      } else {
        // Registration - sign up and create profile
        if (!formData.email || !formData.password || !formData.businessName) {
          throw new Error('Please fill in all required fields');
        }

        // Sign up with email and password
        const result = await signUp({
          email: formData.email,
          password: formData.password,
          name: formData.businessName,
        });



        // Store user ID in localStorage
        localStorage.setItem('userId', result.userId);

        // Create user profile with additional information
        await createUserProfile({
          userId: result.userId,
          businessName: formData.businessName,
          phoneNumber: formData.phoneNumber || undefined,
          physicalAddress: formData.physicalAddress || undefined,
        });

        // Show success message briefly before redirect
        setSuccess('Account created successfully! Redirecting...');

        // Redirect to dashboard after a brief delay
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 1000);
      }
    } catch (err: any) {

      // Provide specific error messages based on the error
      let errorMessage = 'An error occurred. Please try again.';

      if (err.message) {
        if (err.message.includes('User already exists')) {
          errorMessage = 'An account with this email already exists. Please try logging in instead.';
        } else if (err.message.includes('Invalid credentials')) {
          errorMessage = 'Invalid email or password. Please check your credentials and try again.';
        } else if (err.message.includes('User not found')) {
          errorMessage = 'No account found with this email. Please check your email or sign up for a new account.';
        } else if (err.message.includes('network') || err.message.includes('fetch')) {
          errorMessage = 'Network error. Please check your internet connection and try again.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-background text-foreground antialiased">
      <AuthHeader />
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-40">
        <div className="max-w-md mx-auto">
          <h1 className="text-4xl font-bold tracking-tighter mb-4 text-center">{isLogin ? 'Log In' : 'Sign Up'}</h1>

          {/* Debug info */}
          {testResult && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 text-sm">
              ✅ Convex connected: {testResult.message}
            </div>
          )}

          {!testResult && (
            <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4 text-sm">
              ⏳ Connecting to Convex...
            </div>
          )}

          {success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              {success}
            </div>
          )}

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-y-6">
              <div>
                <label htmlFor="email" className="sr-only">Email</label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  placeholder="Email"
                  required
                  className={`block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary rounded-md ${
                    fieldErrors.email ? 'border-red-500 bg-red-50' : 'border-gray-300'
                  }`}
                  value={formData.email}
                  onChange={handleChange}
                />
                {fieldErrors.email && (
                  <p className="mt-1 text-sm text-red-600">{fieldErrors.email}</p>
                )}
              </div>
              <div>
                <label htmlFor="password" className="sr-only">Password</label>
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  placeholder="Password"
                  required
                  className={`block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary rounded-md ${
                    fieldErrors.password ? 'border-red-500 bg-red-50' : 'border-gray-300'
                  }`}
                  value={formData.password}
                  onChange={handleChange}
                />
                {fieldErrors.password && (
                  <p className="mt-1 text-sm text-red-600">{fieldErrors.password}</p>
                )}
              </div>
              {!isLogin && (
                <>
                  <div>
                    <label htmlFor="businessName" className="sr-only">Business Name</label>
                    <input
                      id="businessName"
                      name="businessName"
                      type="text"
                      placeholder="Business Name"
                      required
                      className={`block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary rounded-md ${
                        fieldErrors.businessName ? 'border-red-500 bg-red-50' : 'border-gray-300'
                      }`}
                      value={formData.businessName}
                      onChange={handleChange}
                    />
                    {fieldErrors.businessName && (
                      <p className="mt-1 text-sm text-red-600">{fieldErrors.businessName}</p>
                    )}
                  </div>
                  <div>
                    <label htmlFor="phoneNumber" className="sr-only">Phone Number</label>
                    <input
                      id="phoneNumber"
                      name="phoneNumber"
                      type="text"
                      placeholder="Phone Number (optional)"
                      className={`block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary rounded-md ${
                        fieldErrors.phoneNumber ? 'border-red-500 bg-red-50' : 'border-gray-300'
                      }`}
                      value={formData.phoneNumber}
                      onChange={handleChange}
                    />
                    {fieldErrors.phoneNumber && (
                      <p className="mt-1 text-sm text-red-600">{fieldErrors.phoneNumber}</p>
                    )}
                  </div>
                  <div>
                    <label htmlFor="physicalAddress" className="sr-only">Physical Address (Optional)</label>
                    <input id="physicalAddress" name="physicalAddress" type="text" placeholder="Physical Address (Optional)" className="block w-full shadow-sm py-3 px-4 placeholder-gray-500 focus:ring-primary focus:border-primary border-gray-300 rounded-md" value={formData.physicalAddress} onChange={handleChange} />
                  </div>
                </>
              )}
              {error && <p className="text-red-500 text-sm">{error}</p>}
              <div>
                <Button type="submit" size="lg" className="w-full flex justify-center items-center gap-4" disabled={loading}>
                  {loading ? 'Please wait...' : (isLogin ? 'Log In' : 'Sign Up')}
                </Button>
              </div>
            </div>
          </form>
          <div className="text-center mt-4">
            <button
              type="button"
              onClick={() => {
                setIsLogin(!isLogin);
                setError('');
                setSuccess('');
                setFieldErrors({});
                setFormData({
                  email: '',
                  password: '',
                  businessName: '',
                  phoneNumber: '',
                  physicalAddress: '',
                });
              }}
              className="text-sm text-primary hover:underline"
            >
              {isLogin ? 'Need an account? Sign Up' : 'Already have an account? Log In'}
            </button>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default AuthPage;