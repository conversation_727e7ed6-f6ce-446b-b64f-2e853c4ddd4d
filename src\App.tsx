import React, { useState, useCallback, useEffect } from 'react';
import Sidebar from '../components/layout/Sidebar';
import Header from '../components/layout/Header';
import PageWrapper from '../components/layout/PageWrapper';
import Dashboard from '../pages/Dashboard';
import { Invoices } from '../pages/Invoices';
import Bills from '../pages/Bills';
import Inventory from '../pages/Inventory';
import Reports from '../pages/Reports';
import Settings from '../pages/Settings';
import AIAssistant from '../components/ai/AIAssistant';
import { Page, User, Workspace } from '../types';
import Quotes from '../pages/Quotes';
import Transactions from '../pages/Transactions';
import ChartOfAccounts from '../pages/ChartOfAccounts';
import Team from '../pages/Team';
import Clients from '../pages/Clients';
import Purchases from '../pages/Purchases';
import Suppliers from '../pages/Suppliers';
import { mockData } from '../lib/mockData';
import LandingPage from '../pages/LandingPage';
import ContactPage from '../pages/ContactPage';
import PricingPage from '../pages/PricingPage';
import AuthPage from '../pages/AuthPage';
import { BrowserRouter as Router, Route, Routes, Navigate } from 'react-router-dom';
import { useAuthActions } from "@convex-dev/auth/react";
import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";

const App: React.FC = () => {
  const { signOut } = useAuthActions();
  const currentUser = useQuery(api.users.getCurrentUser);

  return (
    <Router
      future={{
        v7_startTransition: true,
        v7_relativeSplatPath: true,
      }}
    >
      <Routes>
        <Route path="/" element={<LandingPage onLogin={() => {}} />} />
        <Route path="/contact" element={<ContactPage />} />
        <Route path="/pricing" element={<PricingPage />} />
        <Route path="/auth" element={<AuthPage />} />
        <Route
          path="/dashboard"
          element={currentUser ? <DashboardLayout /> : <Navigate to="/auth" />}
        />
      </Routes>
    </Router>
  );
};

const DashboardLayout: React.FC = () => {
  const [currentPage, setCurrentPage] = useState<Page>('Dashboard');
  const [pageContext, setPageContext] = useState<any>(null);
  const [isAiAssistantOpen, setIsAiAssistantOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // This should be replaced with actual data fetching
  const currentUser: User | undefined = mockData.users[0];
  const workspaces: Workspace[] = mockData.workspaces;
  const [activeWorkspaceId, setActiveWorkspaceId] = useState<string | null>(null);

  useEffect(() => {
    if (workspaces && workspaces.length > 0 && !activeWorkspaceId) {
      setActiveWorkspaceId(workspaces[0]._id);
    }
  }, [workspaces, activeWorkspaceId]);

  const handleSwitchWorkspace = (workspaceId: string) => {
    const newWorkspace = workspaces?.find(w => w._id === workspaceId);
    if (newWorkspace) {
      setActiveWorkspaceId(newWorkspace._id);
    }
  };

  const navigate = (page: Page, context?: any) => {
    if (context) {
      setPageContext(context);
    }
    setCurrentPage(page);
    setIsAiAssistantOpen(false);
  };

  const handleOpenTransactionModalFromAI = (prompt: string) => {
    navigate('Transactions', { openAiModalWithPrompt: prompt });
    setIsAiAssistantOpen(false);
  };

  const clearPageContext = useCallback(() => {
    setPageContext(null);
  }, []);

  const renderPage = useCallback(() => {
    if (!activeWorkspaceId) {
      return <div>Loading...</div>;
    }

    const workspaceId = activeWorkspaceId;

    switch (currentPage) {
      case 'Dashboard':
        return <Dashboard workspaceId={workspaceId} />;
      case 'Clients':
        return <Clients workspaceId={workspaceId} navigate={navigate} />;
      case 'Invoices':
        return <Invoices workspaceId={workspaceId} />;
      case 'Quotes':
        return <Quotes workspaceId={workspaceId} />;
      case 'Suppliers':
        return <Suppliers workspaceId={workspaceId} navigate={navigate} />;
      case 'Bills':
        return <Bills workspaceId={workspaceId} context={pageContext} clearPageContext={clearPageContext} />;
      case 'Purchases':
        return <Purchases workspaceId={workspaceId} context={pageContext} clearPageContext={clearPageContext} />;
      case 'Inventory':
        return <Inventory workspaceId={workspaceId} />;
      case 'Transactions':
        return <Transactions workspaceId={workspaceId} context={pageContext} clearPageContext={clearPageContext} />;
      case 'Reports':
        return <Reports workspaceId={workspaceId} />;
      case 'Chart of Accounts':
        return <ChartOfAccounts workspaceId={workspaceId} />;
      case 'Team':
        return <Team workspaceId={workspaceId} />;
      case 'Settings':
        return <Settings workspaceId={workspaceId} />;
      default:
        return <Dashboard workspaceId={workspaceId} />;
    }
  }, [currentPage, activeWorkspaceId, pageContext, clearPageContext, navigate]);

  const activeWorkspace = workspaces?.find(w => w._id === activeWorkspaceId);

  return (
    <div className="flex h-screen bg-gray-50 text-gray-900">
      <Sidebar
        currentPage={currentPage}
        setCurrentPage={setCurrentPage}
        isOpen={isSidebarOpen}
        setIsOpen={setIsSidebarOpen}
      />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header
          user={currentUser || null}
          workspaces={workspaces || []}
          activeWorkspace={activeWorkspace || null}
          onSwitchWorkspace={handleSwitchWorkspace}
          currentPage={currentPage}
          onOpenAiAssistant={() => setIsAiAssistantOpen(true)}
          onOpenSidebar={() => setIsSidebarOpen(true)}
        />
        <PageWrapper>{renderPage()}</PageWrapper>
      </div>
      {activeWorkspace && (
        <AIAssistant
          isOpen={isAiAssistantOpen}
          onClose={() => setIsAiAssistantOpen(false)}
          workspaceId={activeWorkspace._id}
          onNavigate={navigate}
          onOpenTransactionModal={handleOpenTransactionModalFromAI}
        />
      )}
    </div>
  );
};

export default App;